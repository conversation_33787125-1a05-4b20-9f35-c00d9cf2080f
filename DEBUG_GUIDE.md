# 画布召回卡片问题调试指南

## 问题现象
- 手动拖拽笔记到画布上正常工作
- 但是通过AI提问不会自动召回卡片到画布上

## 调试步骤

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 打开浏览器开发者工具
- 打开 http://localhost:3001
- 按F12打开开发者工具
- 切换到Console标签页

### 3. 进入沉思模式并提问
- 点击"沉思模式"
- 在画布中心的输入框中输入问题，例如：
  ```
  产品设计的核心原则是什么？
  ```
- 按Enter提交问题

### 4. 观察控制台日志
按顺序检查以下日志是否出现：

#### 4.1 前端发送请求
```
📡 [输出节点] 准备发送API请求: {
  question: "产品设计的核心原则是什么？",
  knowledgeBaseSize: 20,
  knowledgeBaseSample: [...]
}
```

#### 4.2 后端接收请求
```
🚀 [输出节点API] 收到请求: {
  question: "产品设计的核心原则是什么？...",
  knowledgeBaseSize: 20,
  knowledgeBaseSample: [...]
}
```

#### 4.3 后端召回卡片
```
📤 [召回] 发送召回结果到前端: {
  recalledCardsCount: 3,
  recalledCardsSample: [...]
}
```

#### 4.4 前端接收召回结果
```
📋 [输出节点] 召回卡片详情: {
  count: 3,
  cards: [...]
}
```

#### 4.5 前端处理召回卡片
```
✅ [输出节点] 召回卡片验证通过，调用handleRecalledCards
🎯 [召回处理] 准备添加卡片: [...]
✅ [召回处理] 卡片添加完成: 3
```

### 5. 检查画布渲染
```
🖼️ [DEBUG] 渲染画布卡片: {
  cardsCount: 3,
  cards: [...]
}
```

## 常见问题排查

### 问题1: 没有发送请求
- 检查是否正确进入沉思模式
- 检查输入框是否可见并可输入

### 问题2: 后端没有接收到请求
- 检查网络请求是否成功（Network标签页）
- 检查API端点是否正确

### 问题3: 召回卡片为空
- 检查knowledgeBase是否有数据
- 检查AI匹配逻辑是否正常

### 问题4: 前端接收到数据但没有渲染
- 检查handleRecalledCards是否被调用
- 检查addCards是否正常工作
- 检查knowledgeBaseMap是否包含召回的卡片

## 修复记录

### 已修复的问题：
1. ✅ 双重状态更新冲突
2. ✅ 状态更新时机问题
3. ✅ 数据验证和等待机制
4. ✅ 详细的日志输出

### 当前状态：
- 后端API已增强日志输出
- 前端事件处理已优化
- 数据流已完整跟踪
- 构建成功，无TypeScript错误

## 下一步
如果问题仍然存在，请提供完整的控制台日志，特别是：
1. 是否有错误信息
2. 数据流在哪一步中断
3. 是否有网络请求失败