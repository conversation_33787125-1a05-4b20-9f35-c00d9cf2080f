🧠 沉淀：AI 记忆沉淀器

⸻

一、产品定位与哲学

核心定义

沉淀是为信息焦虑者打造的AI 记忆沉淀器，不是一款需要持续维护的知识管理工具，而是一个能带来「知识心安感」的情绪价值产品。

核心比喻

像 Plex/NAS 之于视频，「沉淀」是知识领域的 Plex：
用户享受的是信息被自动整理、结构化，并最终**拥有一面属于自己的“知识海报墙”**的满足感。

核心理念

记录的过程 × 拥有的感觉 > 回顾的结果

⸻

二、产品战略与差异化

维度	沉淀	Notion / Obsidian	NotebookLM / IMA Copilot
知识组织	全局卡片式知识库，支持自由流动与加工	手动笔记管理，整理负担重	以笔记本为单位，内容隔离
AI 能力	支持结构化整理、语义召回、多模态加工	无原生 AI 能力	有 RAG，难跨笔记本联动
用户角色	信息消费者（非重度整理者）	知识苦行僧	AI 辅助阅读者


⸻

三、产品形态与核心流程

1️⃣ 收集 → 沉淀（AI笔记）
	•	浏览器插件快速收藏内容（网页、PDF、微信图文/图片）
	•	收集后不唤醒主应用，仅进入“收集箱”中
	•	AI 自动生成结构化卡片（含标题、摘要、大纲、笔记）
	•	用户可划词精修（借鉴 AI IDE 思路），无需手动编辑整段笔记
	•	卡片保存至全局知识库，具备标签/时间/内容类型等元信息

2️⃣ 召回 → 画布（知识再组织）
	•	用户提问或新建任务，进入空白画布
	•	中央生成 AI 问答节点，自动召回相关知识卡片分布在四周
	•	卡片与节点自动连线，表示当前 AI 回答所引用内容
	•	后续对话过程中，系统持续推荐更多卡片浮动出现，供用户拖入

3️⃣ 加工 → 输出（多模态内容生成）
	•	画布中支持添加输出节点（如：PPT 节点 / 小红书图文节点 / 播客节点）
	•	每种输出类型是一个交互模块，用户可将卡片拖入连线，触发内容生成
	•	每个节点支持需求提示（如“帮我输出一个可分享的长图”）
	•	生成结果可导出或保存回知识库

⸻

四、知识库结构与交互逻辑

存储结构：非文件夹式 → 多维索引式
	•	不采用传统文件夹结构
	•	使用 标签 / 时间 / 内容类型 /模糊搜索 等方式进行快速筛选
	•	知识库是用户个人全局向量数据库，所有内容可跨任务召回

交互方式：视频剪辑式工作流画布
	•	类似剪辑软件：本地素材库（知识卡片） → 项目素材篮（召回区） → 时间线画布（加工与输出）
	•	每次加工过程都是一次新的创作任务（任务画布）
	•	支持多个卡片在一画布中联动加工、被引用、组合生成新作品

⸻

五、AI 原生交互设计亮点
	•	结构化笔记编辑：借鉴 Cursor 的 Agent 模式，AI 通过对话生成 diff 自动修改笔记
	•	卡片召回 + 连线可视化：对话自动带出引用卡片并连线展示上下文关联
	•	划词局部问答：用户在原文或卡片中划词，即可就该片段进行问答或重写
	•	输出节点交互：每类输出都是“拖入卡片 → 生成内容 → 调整风格 → 导出”的流程


⸻

六、开发计划与进度追踪

### 第一阶段：收集 → 沉淀（AI笔记）

| 功能模块 | 详细说明 | 状态 | 优先级 |
|---------|---------|------|--------|
| 输入框+文本内容导入 | 支持文本内容拖拽上传和粘贴输入 | ✅ 已完成 | P0 |
| AI自动生成简介和导读 | 基于输入内容自动生成文档摘要和关键信息 | ✅ 已完成 | P1 |
| AI 自动生成笔记 | 智能结构化笔记生成，包含标题、大纲、要点等 | ✅ 已完成 | P1 |
| Ask/Agent 双模式切换 | 支持问答模式和智能代理模式的切换 | ✅ 已完成 | P1 |
| Agent 模式自动修改笔记 | AI 代理模式下基于对话内容自动优化笔记 | ✅ 已完成 | P0 |
| 知识/笔记保存至数据库 | 持久化存储用户的知识库和笔记内容 | ⏳ 未开始 | P1 |
| 知识/笔记AI打标签 | 自动标签分类，支持标签/时间/内容类型筛选 | ⏳ 未开始 | P1 |
| 多模态知识导入 | 支持链接、音视频、PPT、PDF等多种格式导入 | ⏳ 未开始 | P1 |
| 收集箱功能 | 临时存放待处理的信息收集 | ⏳ 未开始 | P1 |
| 浏览器插件快速导入 | 浏览器插件一键收藏到收集箱 | ⏳ 未开始 | P1 |
| 划词询问精修部分笔记 | 选中文本片段进行局部问答和内容优化 | ⏳ 未开始 | P1 |
| 笔记手动修改功能 | 用户可手动编辑和调整AI生成的笔记 | ⏳ 未开始 | P1 |
| Tab 自动补全笔记 | 智能补全和联想输入功能 | ⏳ 未开始 | P1 |

### 第二阶段：沉思 → 输出（知识再组织输出）

| 功能模块 | 详细说明 | 状态 | 优先级 |
|---------|---------|------|--------|
| 双模式切换，沉思模式框架 | 收集模式与沉思模式的切换界面框架 | ✅ 已完成 | P0 |
| 笔记管理侧边栏 | 知识库管理和笔记导航界面 | ✅ 已完成 | P0 |
| 空白画布功能框架 | 任务画布的基础框架和交互逻辑 | ✅ 已完成 | P0 |
| 画布初始对话召回功能 | 画布中基于问题自动召回相关知识卡片 | 🔄 开发中 | P0 |
| 生成 AI 问答节点 | 画布中央的AI对话节点生成和管理 | ⏳ 未开始 | P0 |
| 初次自动召回相关知识 | 首次进入画布时的智能知识推荐 | ⏳ 未开始 | P0 |
| 输出节点功能（HTML网页） | 支持HTML网页格式的内容输出节点 | ⏳ 未开始 | P0 |
| 卡片与节点连线表现 | 知识卡片与节点间的可视化连线效果 | ⏳ 未开始 | P1 |
| 增加多模态节点 | 支持小红书图文、播客等多种输出格式 | ⏳ 未开始 | P1 |
| 系统自动召回可选卡片 | 持续推荐相关卡片供用户拖入画布 | ⏳ 未开始 | P1 |

### 开发进度说明

**已完成模块 (7/20)**：
- 基础的文本输入和AI分析能力
- 双模式交互框架（Ask/Agent）
- 基础的笔记生成和编辑能力
- 沉思模式的UI框架

**开发中模块 (1/20)**：
- 画布召回功能的核心逻辑

**待开发模块 (12/20)**：
- 数据持久化和标签系统
- 多模态内容支持
- 高级画布交互功能

**整体进度**：40% 完成（核心MVP功能基本就绪）
