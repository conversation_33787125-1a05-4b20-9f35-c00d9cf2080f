{"permissions": {"allow": ["Bash(npx create-next-app:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install)", "Bash(npm run dev:*)", "Bash(npm view:*)", "Bash(npm install:*)", "Bash(npm run build:*)", "Bash(npm run type-check:*)", "Bash(npm run typecheck:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(pkill:*)", "Bash(rm:*)", "Bash(npm run:*)", "Bash(grep:*)", "Bash(rg:*)", "Bash(find:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "<PERSON><PERSON>(cat:*)", "Bash(cp:*)", "Bash(/dev/null)", "Bash(node:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(claude doctor)", "<PERSON>sh(claude config show)", "<PERSON><PERSON>(claude config list)", "Bash(npm config:*)", "Bash(curl -X POST http://localhost:3001/api/canvas-question-stream )", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\"\"question\"\":\"\"如何进行有效的用户调研？\"\",\"\"knowledgeBase\"\":[]}' )", "Bash(--no-buffer)", "<PERSON><PERSON>(true)", "Bash(npx next:*)", "<PERSON><PERSON>(killall:*)"], "deny": []}}