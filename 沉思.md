# 沉思模式 - 产品架构设计文档

> **目标**：在现有沉淀模式基础上，新增沉思模式，实现知识库召回 + 画布工作 + AI对话的完整体验。

---

## 🎯 **产品双模式架构**

### **模式定位**
- **沉淀模式** (已完成)：文件上传 → AI分析 → 结构化笔记生成 → diff编辑
- **沉思模式** (待开发)：知识库管理 → 智能召回 → 画布工作 → 深度对话

### **模式切换设计**
```
最左侧竖向按钮栏：
┌─┐
│📖│ ← 沉淀模式 (当前模式)
├─┤
│📄│
├─┤
│🧠│ ← 沉思模式 (新增模式)
└─┘
```

---

## 🧠 **沉思模式界面设计**

### **整体布局结构**
```
┌─┬─────────────┬─────────────────────────────────────────────────────┐
│🧠│ 🔍 搜索框    │                                                     │
├─┼─────────────┤               🎨 画布工作区                          │
│ │ 📂 标签分类   │                                                     │
│ │ ├─ 技术(156) │     ┌─────────────────────────────────────┐         │
│ │ ├─ 商业(89)  │     │ 🔍 输入框                            │         │
│ │ └─ 学习(67)  │     │ "问我任何问题..."                    │         │
│ │              │     │                                     │         │
│ │ 📄 笔记列表   │     │ [提交后生成问题节点和召回卡片]       │         │
│ │ ├─ AI产品设计 │     └─────────────────────────────────────┘         │
│ │ ├─ 用户体验   │                                                     │
│ │ ├─ 技术架构   │                                                     │
│ │ └─ ...       │                                                     │
└─┴─────────────┴─────────────────────────────────────────────────────┘
```

### **三栏布局说明**
- **左栏 (25%)**：笔记管理区域 (搜索 + 分类 + 列表)
- **右栏 (75%)**：画布工作区域 (问题节点 + 笔记卡片 + 推荐区)

---

## 📝 **左侧笔记管理区域**

### **搜索功能 - 浮动卡片设计**

#### **视觉设计参考**
参考项目中FloatingTOC组件的超轻盈毛玻璃效果：

```css
/* 搜索卡片样式 - 参考 FloatingTOC 的毛玻璃效果 */
.search-card {
  /* 毛玻璃效果 */
  backdrop-filter: blur(32px) saturate(150%);
  -webkit-backdrop-filter: blur(32px) saturate(150%);
  background: rgba(255, 255, 255, 0.03);
  
  /* 细边框 */
  border: 0.5px solid rgba(255, 255, 255, 0.08);
  
  /* 圆角设计 - 更加圆润 */
  border-radius: 24px;
  
  /* 多层阴影创造深度 */
  box-shadow: 
    0 24px 64px rgba(0, 0, 0, 0.03),
    0 12px 32px rgba(0, 0, 0, 0.02),
    0 6px 16px rgba(0, 0, 0, 0.01),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  
  /* 内边距和外边距 */
  margin: 20px;
  padding: 24px;
  
  /* 动画过渡 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 折叠状态 */
.search-card.collapsed {
  width: 56px;
  height: 56px;
  padding: 16px;
  margin-left: 8px;
  cursor: pointer;
  border-radius: 28px;
}

/* 悬浮效果 */
.search-card:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 32px 72px rgba(0, 0, 0, 0.04),
    0 16px 40px rgba(0, 0, 0, 0.03),
    0 8px 20px rgba(0, 0, 0, 0.02);
}
```

#### **展开状态设计**
```
┌──────────────────────────────────────┐
│                                      │  <- 上边距 20px
│  ╭──────────────────────────────╮    │
│  │  🔍  搜索笔记...             │    │  <- 毛玻璃卡片
│  │  ─────────────────────────   │    │
│  │  💡 试试: #标签 或 "关键词"   │    │
│  ╰──────────────────────────────╯    │
│                                      │  <- 下边距 20px
├──────────────────────────────────────┤

细节：
- 超轻透明背景：rgba(255, 255, 255, 0.03)
- 高斯模糊：blur(32px) + saturate(150%)
- 圆润边角：border-radius: 24px
- 悬浮阴影：多层叠加创造深度感
```

#### **折叠状态设计**
```
┌──────────────────────────────────────┐
│ ◉ 🔍                                 │  <- 折叠到边缘的搜索按钮
├──────────────────────────────────────┤

动画效果：
- 平滑收缩：cubic-bezier(0.4, 0, 0.2, 1)
- 图标旋转：rotate(360deg) 0.6s
- 透明度变化：从 1 到 0.8
```

#### **搜索交互细节**
```typescript
interface SearchCardProps {
  isExpanded: boolean
  onToggle: () => void
  onSearch: (query: string, filters: SearchFilters) => void
}

interface SearchFilters {
  mode: 'keyword' | 'tag' | 'semantic'
  tags: string[]
  dateRange?: { from: Date; to: Date }
  domains?: string[]
}

// 搜索语法支持
const searchSyntax = {
  keyword: '"ai产品"',              // 精确匹配
  tag: '#技术 #前端',               // 标签组合
  mixed: 'react #前端',             // 混合搜索
  semantic: '~如何学习编程',         // 语义搜索（~ 前缀）
  exclude: 'react -native',         // 排除关键词
  domain: '@技术/前端',             // 领域搜索
}
```

#### **动画效果**
```css
/* 淡入上升效果 - 参考 FloatingTOC */
@keyframes searchCardFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 折叠动画 */
@keyframes collapseToEdge {
  0% {
    width: 100%;
    opacity: 1;
  }
  50% {
    opacity: 0.8;
    transform: scale(0.95);
  }
  100% {
    width: 56px;
    opacity: 1;
    transform: translateX(-80%);
  }
}
```

### **标签分类系统**
```
📂 按行业分类
├─ 💼 技术 (156)
│   ├─ 前端开发 (45)
│   ├─ 后端开发 (38)
│   └─ AI/ML (73)
├─ 📊 商业 (89)
│   ├─ 产品设计 (34)
│   ├─ 用户体验 (28)
│   └─ 运营策略 (27)
└─ 📚 学习 (67)

📅 按时间分类
├─ 今天 (5)
├─ 本周 (23)
├─ 本月 (78)
└─ 更早 (234)

🏷️ 智能分类
├─ 常用 (34)
├─ 收藏 (12)
├─ 最近访问 (28)
└─ 重要程度高 (19)
```

### **笔记列表展示**
```
📄 《AI产品设计思考》
💡 本文探讨了AI产品设计的核心原则和用户体验考虑...
🏷️ #产品设计 #人工智能 #用户体验
📅 2024-01-15 | 📖 5分钟阅读 | 👁️ 已阅读3次

📄 《用户体验设计原则》
💡 好的用户体验设计应该遵循以下基本原则和方法...
🏷️ #用户体验 #设计原则 #交互设计
📅 2024-01-12 | 📖 3分钟阅读 | ⭐ 已收藏

[更多笔记...]
```

---

## 🎨 **画布工作区域**

### **初始状态**
```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                                                             │
│              ┌─────────────────────────────────┐             │
│              │ 🔍 输入框                        │             │
│              │ 问我任何问题...                  │             │
│              │                                 │             │
│              │ 💡 例如：如何设计好的AI产品？    │             │
│              └─────────────────────────────────┘             │
│                                                             │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **提交问题后的状态**
```
┌─────────────────────────────────────────────────────────────┐
│ ┌─────────────────────────────────────┐                     │
│ │ 💬 问题节点                          │                     │
│ │ "如何设计好的AI产品？"                │                     │
│ │ ──────────────────────────────────── │                     │
│ │ 🤖 AI: 基于你的知识库，我总结了      │                     │
│ │ 几个核心要点：透明性、可控性...      │                     │
│ │                                     │                     │
│ │ 📝 继续提问...                       │                     │
│ └─────────────────────────────────────┘                     │
│        ↓ 连接线      ↓ 连接线      ↓ 连接线                   │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│ │ 📄 笔记卡片1 │ │ 📄 笔记卡片2 │ │ 📄 笔记卡片3 │             │
│ │ AI产品设计   │ │ 用户体验     │ │ 产品策略     │             │
│ │ 思考         │ │ 设计原则     │ │ 制定方法     │             │
│ │ #产品 #AI   │ │ #UX #设计   │ │ #策略 #产品 │             │
│ │ 🔍 双击展开  │ │ 🔍 双击展开  │ │ 🔍 双击展开  │             │
│ └─────────────┘ └─────────────┘ └─────────────┘             │
│                                                             │
│ 🔍 推荐召回区：                                              │
│ [设计方法论] [AI伦理] [产品迭代] ← 可拖拽到画布加入上下文     │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔄 **核心交互流程**

### **1. 首次召回流程**
```
用户输入问题
     ↓
AI分析问题语义和关键词
     ↓
从知识库中召回最相关的笔记 (通常3-5个)
     ↓
生成问题节点并连接召回的笔记卡片
     ↓
AI基于召回的笔记内容生成回答
     ↓
在推荐区显示更多相关笔记
```

### **2. 持续对话流程**
```
用户在问题节点继续提问
     ↓
AI以当前连接的笔记作为上下文
     ↓
生成更精准的回答
     ↓
触发新一轮召回，推荐更多相关内容
```

### **3. 上下文扩展流程**
```
用户从推荐区拖拽笔记到画布
     ↓
自动连接到问题节点
     ↓
成为新的上下文参与后续对话
     ↓
AI回答质量进一步提升
```

### **4. 笔记详情查看**
```
双击任意笔记卡片
     ↓
打开笔记详情窗口 (占屏幕70%)
     ↓
左侧60%: 结构化笔记内容 (可切换原文)
右侧40%: 针对该笔记的AI助手
     ↓
支持编辑模式 (类似当前的沉淀模式)
```

---

## 🗂️ **标签分类系统设计**

### **AI智能标签生成**
```typescript
// 标签生成维度
interface TagSystem {
  contentType: {
    docType: "文章" | "论文" | "报告" | "笔记" | "教程"
    mediaType: "文本" | "图片" | "视频" | "PDF"
    sourceType: "网页" | "本地文件" | "导入"
  }
  
  domain: {
    primary: string        // 主要领域：技术、商业、学术
    secondary: string[]    // 次要领域：前端、产品设计
    keywords: string[]     // 关键词：React、用户体验
  }
  
  cognitive: {
    complexity: "入门" | "中级" | "高级" | "专家"
    knowledgeType: "事实" | "概念" | "原理" | "方法" | "案例"
    abstractLevel: "具体" | "抽象" | "理论" | "实践"
  }
  
  functional: {
    purpose: "学习资料" | "工作参考" | "灵感来源" | "项目资料"
    importance: "核心" | "重要" | "一般" | "参考"
    urgency: "紧急" | "重要不紧急" | "一般" | "存档"
  }
}
```

### **动态标签文件夹**
```typescript
// 支持复杂的标签查询组合
interface SmartFolder {
  name: string
  tagQuery: string[]
  logic: "AND" | "OR" | "NOT"
  
  // 示例
  {
    name: "技术-前端-React",
    tagQuery: ["技术", "前端", "React"],
    logic: "AND"
  },
  {
    name: "重要学习资料",
    tagQuery: ["学习资料", "重要"],
    logic: "AND"
  }
}
```

---

## 💾 **数据结构设计**

### **笔记文档结构**
```typescript
interface Note {
  id: string
  title: string
  content: string              // 结构化笔记内容
  originalContent: string      // 原始文档内容
  summary: string              // AI生成的摘要
  tags: TagSystem              // 结构化标签
  createdAt: Date
  updatedAt: Date
  lastAccessedAt: Date         // 最后访问时间
  accessCount: number          // 访问次数
  isStarred: boolean          // 是否收藏
  metadata: {
    wordCount: number
    readingTime: number        // 预估阅读时间(分钟)
    sourceType: 'upload' | 'import' | 'manual'
    sourceUrl?: string         // 如果来自网页
    fileType?: string          // 原始文件类型
  }
  embedding?: number[]         // 向量嵌入(用于语义搜索)
}
```

### **画布状态结构**
```typescript
interface Canvas {
  id: string
  title?: string               // 画布标题(可选)
  questionNode: QuestionNode
  connectedNotes: ConnectedNote[]
  recommendedNotes: Note[]
  viewport: {
    x: number
    y: number
    zoom: number
  }
  createdAt: Date
  updatedAt: Date
}

interface QuestionNode {
  id: string
  question: string
  aiResponse: string
  conversation: Message[]
  position: { x: number, y: number }
}

interface ConnectedNote {
  noteId: string
  position: { x: number, y: number }
  size: 'small' | 'medium' | 'large'
  connectionStrength: number   // 与问题的相关性评分
}

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  contextNoteIds: string[]     // 此消息的上下文笔记
}
```

### **搜索索引结构**
```typescript
interface SearchIndex {
  noteId: string
  title: string
  content: string
  tags: string[]
  fullText: string             // 标题+内容+标签的全文本
  embedding: number[]          // 语义搜索向量
  metadata: {
    boost: number             // 搜索权重加权
    popularity: number        // 基于访问频率的热度
    recency: number          // 基于时间的新鲜度
  }
}
```

---

## 🎯 **开发优先级与里程碑**

### **Phase 1: 基础设施 (Week 1-2)**
1. **模式切换功能**
   - 左侧竖向按钮栏
   - 沉淀模式与沉思模式切换

2. **笔记持久化系统**
   - IndexedDB数据库设计
   - 从沉淀模式保存笔记到数据库
   - 基础的CRUD操作

3. **沉思模式界面框架**
   - 三栏布局搭建
   - 基础组件结构

### **Phase 2: 核心功能 (Week 3-4)**
4. **标签系统**
   - AI标签生成集成
   - 标签分类展示
   - 智能文件夹功能

5. **笔记管理功能**
   - 笔记列表展示
   - 基础搜索功能
   - 分类筛选

6. **画布基础功能**
   - 中央输入框
   - 问题节点生成
   - 笔记卡片展示

### **Phase 3: 智能功能 (Week 5-6)**
7. **智能召回系统**
   - 基于语义的笔记召回算法
   - 相关性评分
   - 推荐笔记功能

8. **画布交互**
   - 节点连线可视化
   - 拖拽交互
   - 卡片尺寸调整

9. **AI对话集成**
   - 问题节点的对话功能
   - 上下文管理
   - 流式回答

### **Phase 4: 体验优化 (Week 7-8)**
10. **笔记详情窗口**
    - 双击打开详情
    - 编辑模式集成
    - 针对性AI助手

11. **高级搜索**
    - 语义搜索
    - 复杂查询语法
    - 搜索结果排序

12. **性能与体验优化**
    - 虚拟化渲染
    - 缓存策略
    - 响应式设计

---

## 🛠️ **具体实现方案**

### **第一步：创建沉思模式的基础架构和模式切换功能**

#### **1. 模式切换按钮设计**

根据项目现有架构，在TabBar组件中添加模式切换功能：

```typescript
// 1. 定义模式类型
export type AppMode = 'sediment' | 'thinking'  // 沉淀模式 | 沉思模式

// 2. 模式配置
const modeConfig = {
  sediment: {
    icon: '📝',
    label: '沉淀模式',
    description: '文档分析与笔记生成',
    shortcut: '⌘+1'
  },
  thinking: {
    icon: '🧠',
    label: '沉思模式',
    description: '知识召回与深度对话',
    shortcut: '⌘+2'
  }
}
```

#### **2. 修改TabBar组件**

在 `/src/components/TabBar.tsx` 中添加模式切换按钮：

```tsx
// TabBar组件改造
export function TabBar({ currentMode, onModeChange }) {
  return (
    <div className="tab-bar">
      {/* 左侧：模式切换区域 */}
      <div className="mode-switcher">
        <button 
          className={`mode-btn ${currentMode === 'sediment' ? 'active' : ''}`}
          onClick={() => onModeChange('sediment')}
          title="沉淀模式 (⌘+1)"
        >
          📝
        </button>
        <button 
          className={`mode-btn ${currentMode === 'thinking' ? 'active' : ''}`}
          onClick={() => onModeChange('thinking')}
          title="沉思模式 (⌘+2)"
        >
          🧠
        </button>
      </div>
      
      {/* 分隔线 */}
      <div className="mode-divider" />
      
      {/* 右侧：原有的标签页区域（仅在沉淀模式显示） */}
      {currentMode === 'sediment' && (
        <div className="tabs-area">
          {/* 原有的tabs内容 */}
        </div>
      )}
    </div>
  )
}
```

#### **3. 模式切换样式**

```css
/* 模式切换按钮样式 */
.mode-switcher {
  display: flex;
  gap: 4px;
  padding: 4px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 10px;
  margin-right: 12px;
}

.mode-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.mode-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.mode-btn.active {
  background: white;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.mode-btn.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #3b82f6;
  border-radius: 50%;
}

.mode-divider {
  width: 1px;
  height: 32px;
  background: rgba(0, 0, 0, 0.08);
  margin: 0 8px;
  align-self: center;
}
```

#### **4. 主页面状态管理**

在 `/src/app/page.tsx` 中添加模式状态：

```typescript
export default function Home() {
  // 添加模式状态
  const [appMode, setAppMode] = useState<AppMode>('sediment')
  
  // 处理模式切换
  const handleModeChange = (mode: AppMode) => {
    setAppMode(mode)
    // 可以添加动画效果或其他逻辑
  }
  
  // 快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.metaKey || e.ctrlKey) {
        if (e.key === '1') {
          e.preventDefault()
          handleModeChange('sediment')
        } else if (e.key === '2') {
          e.preventDefault()
          handleModeChange('thinking')
        }
      }
    }
    
    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [])
  
  return (
    <div className="app">
      <TabBar 
        currentMode={appMode}
        onModeChange={handleModeChange}
        // ... 其他props
      />
      
      {/* 根据模式渲染不同内容 */}
      <div className="app-content">
        {appMode === 'sediment' ? (
          <SedimentMode />  // 原有的沉淀模式内容
        ) : (
          <ThinkingMode />  // 新的沉思模式内容
        )}
      </div>
    </div>
  )
}
```

#### **5. 创建沉思模式容器组件**

创建 `/src/components/thinking-mode/ThinkingMode.tsx`：

```typescript
export function ThinkingMode() {
  return (
    <div className="thinking-mode">
      {/* 三栏布局 */}
      <div className="thinking-layout">
        {/* 左侧：笔记管理 */}
        <aside className="notes-sidebar">
          <SearchCard />
          <NotesTree />
        </aside>
        
        {/* 右侧：画布工作区 */}
        <main className="canvas-workspace">
          <CanvasArea />
        </main>
      </div>
    </div>
  )
}
```

#### **6. 布局样式**

```css
.thinking-mode {
  width: 100%;
  height: calc(100vh - 60px); /* 减去TabBar高度 */
  overflow: hidden;
}

.thinking-layout {
  display: grid;
  grid-template-columns: 320px 1fr;  /* 左侧320px，右侧自适应 */
  height: 100%;
  gap: 0;
}

.notes-sidebar {
  background: #f8fafc;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  overflow-y: auto;
  overflow-x: hidden;
}

.canvas-workspace {
  background: #ffffff;
  position: relative;
  overflow: hidden;
}
```

#### **7. 状态持久化**

使用 localStorage 记住用户的模式选择：

```typescript
// 读取保存的模式
const savedMode = localStorage.getItem('app-mode') as AppMode || 'sediment'
const [appMode, setAppMode] = useState<AppMode>(savedMode)

// 保存模式选择
const handleModeChange = (mode: AppMode) => {
  setAppMode(mode)
  localStorage.setItem('app-mode', mode)
}
```

#### **8. 平滑过渡动画**

```css
/* 模式切换动画 */
.app-content {
  position: relative;
  height: calc(100vh - 60px);
  overflow: hidden;
}

.sediment-mode,
.thinking-mode {
  position: absolute;
  inset: 0;
  animation: modeTransition 0.3s ease-out;
}

@keyframes modeTransition {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

#### **9. 文件结构规划**

```
src/
├── components/
│   ├── TabBar.tsx              (修改：添加模式切换)
│   └── thinking-mode/          (新增目录)
│       ├── ThinkingMode.tsx    (沉思模式主容器)
│       ├── SearchCard.tsx      (搜索卡片组件)
│       ├── NotesTree.tsx       (笔记列表组件)
│       ├── CanvasArea.tsx      (画布工作区)
│       └── index.ts            (统一导出)
├── app/
│   └── page.tsx                (修改：添加模式状态管理)
└── types/
    └── thinking.ts             (新增：沉思模式相关类型定义)
```

---

## 🔧 **技术实现要点**

### **前端技术栈**
- **React 18 + TypeScript**: 保持与沉淀模式一致
- **TanStack Query**: 数据获取和缓存
- **React Flow**: 画布节点和连线可视化
- **Fuse.js**: 客户端模糊搜索
- **IndexedDB**: 本地数据存储

### **AI集成**
- **标签生成**: 复用现有的AI分析接口
- **语义搜索**: 集成向量嵌入和相似度计算
- **智能召回**: 基于问题语义匹配相关笔记
- **对话管理**: 扩展现有的聊天流式接口

### **数据管理**
- **本地优先**: 所有数据存储在IndexedDB
- **增量同步**: 支持未来的云端同步功能
- **数据迁移**: 平滑的版本升级路径

---

## 🎨 **UI/UX设计原则**

### **一致性**
- 与沉淀模式保持视觉风格一致
- 复用现有的组件库和设计tokens
- 统一的交互模式和快捷键

### **渐进式复杂度**
- 初次使用简单直观(中央输入框)
- 高级功能逐步展现(标签分类、画布操作)
- 支持用户自定义工作流程

### **响应式设计**
- 适配不同屏幕尺寸
- 灵活的布局调整
- 键盘友好的操作

### **性能优先**
- 虚拟化长列表
- 懒加载和增量渲染
- 智能的数据预取

---

## 📝 **验收标准**

### **基本功能验收**
- [ ] 可以在沉淀模式和沉思模式之间切换
- [ ] 沉淀模式生成的笔记可以保存到知识库
- [ ] 可以通过标签和搜索找到保存的笔记
- [ ] 可以在画布中提问并召回相关笔记
- [ ] 可以与问题节点进行持续对话
- [ ] 可以双击笔记查看详情和编辑

### **用户体验验收**
- [ ] 界面响应流畅，无明显卡顿
- [ ] 搜索结果准确且相关性高
- [ ] 画布操作直观，拖拽体验良好
- [ ] AI回答质量高，引用的笔记相关性强
- [ ] 笔记详情窗口功能完整，编辑体验佳

### **技术质量验收**
- [ ] 代码结构清晰，组件复用性好
- [ ] 数据存储稳定，无数据丢失
- [ ] 性能指标达标(页面加载<2s，搜索响应<500ms)
- [ ] 错误处理完善，用户友好的错误提示

---

## 🚀 **开发策略：Mock数据优先**

### **渐进式实施方案**

#### **Phase 0: Mock数据快速原型（推荐先行）**
使用预设的高质量假数据，快速实现沉思模式的完整体验：

**优势：**
- ✅ 可以快速搭建沉思模式的完整界面
- ✅ 专注于画布交互和召回体验的打磨
- ✅ 避免被数据持久化的复杂性阻塞
- ✅ 更早获得用户反馈

**Mock数据设计：**
```typescript
// mockData.ts
export const mockNotes: Note[] = [
  {
    id: '1',
    title: 'AI产品设计思考',
    content: '# AI产品设计思考\n\n## 核心原则\n1. 透明性...',
    summary: '本文探讨了AI产品设计的核心原则...',
    tags: {
      domain: { primary: '产品设计', secondary: ['AI', 'UX'] },
      contentType: { docType: '文章' }
    },
    createdAt: new Date('2024-01-15'),
    relatedNoteIds: ['2', '5', '8'] // 预设关联关系
  },
  // 准备20-30个涵盖不同领域的高质量假数据
]
```

#### **最小化保存功能（可选）**
在沉淀模式添加简单的保存按钮：
```typescript
const handleSaveToLibrary = async () => {
  const note = {
    id: generateId(),
    title: extractTitleFromMarkdown(notes),
    content: notes,
    originalContent: originalContent,
    summary: generateSummary(notes),
    tags: await generateTagsWithAI(notes),
    createdAt: new Date()
  }
  
  // 暂时保存到localStorage
  const saved = JSON.parse(localStorage.getItem('notes') || '[]')
  saved.push(note)
  localStorage.setItem('notes', JSON.stringify(saved))
  
  toast.success('已保存到知识库')
}
```

---

## 🔍 **智能召回算法设计**

### **Phase 1: 关键词匹配算法（立即实现）**
```typescript
// 简单但有效的召回算法
function recallNotes(question: string, allNotes: Note[]): Note[] {
  // 1. 提取关键词
  const keywords = extractKeywords(question)
  
  // 2. 计算相关性分数
  const scoredNotes = allNotes.map(note => {
    let score = 0
    
    // 标题匹配（权重高）
    keywords.forEach(keyword => {
      if (note.title.toLowerCase().includes(keyword.toLowerCase())) {
        score += 10
      }
    })
    
    // 内容匹配
    keywords.forEach(keyword => {
      const matches = (note.content.match(new RegExp(keyword, 'gi')) || []).length
      score += matches * 2
    })
    
    // 标签匹配
    keywords.forEach(keyword => {
      if (note.tags.domain.primary.includes(keyword) || 
          note.tags.domain.secondary.includes(keyword)) {
        score += 5
      }
    })
    
    // 时间衰减（最近的笔记略微加分）
    const daysSinceCreated = (Date.now() - note.createdAt.getTime()) / (1000 * 60 * 60 * 24)
    score += Math.max(0, 5 - daysSinceCreated / 30)
    
    return { note, score }
  })
  
  // 3. 返回得分最高的笔记
  return scoredNotes
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, 5)
    .map(item => item.note)
}
```

### **Phase 2: 混合召回（Mock数据阶段）**
```typescript
// 结合预设关联和AI分析
async function hybridRecall(question: string, notes: Note[]): Promise<Note[]> {
  // 1. 让AI分析问题意图
  const analysis = await analyzeQuestion(question)
  
  // 2. 关键词召回
  const keywordResults = keywordBasedRecall(analysis.keywords, notes)
  
  // 3. 利用预设的关联关系
  const relatedResults = keywordResults.flatMap(note => 
    note.relatedNoteIds?.map(id => notes.find(n => n.id === id)).filter(Boolean) || []
  )
  
  // 4. 合并去重并排序
  const combined = [...keywordResults, ...relatedResults]
  const unique = Array.from(new Map(combined.map(n => [n.id, n])).values())
  
  return unique.slice(0, 5)
}

// AI分析问题
async function analyzeQuestion(question: string) {
  const prompt = `
分析用户问题，提取关键信息：
问题：${question}

返回JSON格式：
{
  "intent": "问题的意图",
  "keywords": ["关键词1", "关键词2"],
  "suggestedTags": ["可能相关的标签"],
  "domain": "问题所属领域"
}
`
  return await callAI(prompt)
}
```

### **Phase 3: 向量嵌入（后期优化）**
```typescript
// 使用轻量级本地模型（可选）
import { pipeline } from '@xenova/transformers'

const embedder = await pipeline(
  'feature-extraction',
  'Xenova/all-MiniLM-L6-v2' // 轻量级模型
)

// 或使用OpenAI Embedding API（需要成本）
async function generateEmbedding(text: string): Promise<number[]> {
  const response = await fetch('/api/embedding', {
    method: 'POST',
    body: JSON.stringify({ text })
  })
  return response.json()
}
```

---

## 📊 **Mock数据内容规划**

### **准备的笔记主题（覆盖不同领域）**

**技术类：**
- React Hooks最佳实践
- 前端性能优化指南
- TypeScript进阶技巧
- 微服务架构设计
- 数据库索引优化

**产品设计类：**
- AI产品设计思考
- 用户体验设计原则
- 产品需求文档模板
- 敏捷开发实践
- 用户研究方法论

**商业策略类：**
- 商业模式画布
- 增长黑客策略
- 市场营销基础
- 创业公司管理
- 投资基础知识

**学习成长类：**
- 如何高效学习
- 时间管理方法
- 个人知识管理
- 职业发展规划
- 心理学基础

### **预设关联关系**
```typescript
const relationshipMap = {
  'React Hooks最佳实践': ['前端性能优化指南', 'TypeScript进阶技巧'],
  'AI产品设计思考': ['用户体验设计原则', '产品需求文档模板'],
  '商业模式画布': ['创业公司管理', '市场营销基础'],
  // ... 更多关联关系
}
```

---

## 🔧 **数据服务接口设计**

```typescript
// 数据服务接口（便于后期替换）
interface DataService {
  // 基础CRUD
  getNotes(): Promise<Note[]>
  getNote(id: string): Promise<Note | null>
  saveNote(note: Note): Promise<void>
  updateNote(id: string, note: Partial<Note>): Promise<void>
  deleteNote(id: string): Promise<void>
  
  // 搜索和召回
  searchNotes(query: string): Promise<Note[]>
  recallNotes(question: string): Promise<Note[]>
  getRelatedNotes(noteId: string): Promise<Note[]>
  
  // 标签管理
  getAllTags(): Promise<string[]>
  getNotesByTag(tag: string): Promise<Note[]>
}

// Mock实现
class MockDataService implements DataService {
  private notes = [...mockNotes]
  
  async getNotes() {
    return this.notes
  }
  
  async recallNotes(question: string) {
    return hybridRecall(question, this.notes)
  }
  
  // ... 其他方法的Mock实现
}

// 真实实现（后期）
class IndexedDBService implements DataService {
  // 使用IndexedDB的真实实现
}

// 依赖注入
export const dataService = process.env.USE_MOCK_DATA 
  ? new MockDataService() 
  : new IndexedDBService()
```

---

## 📅 **调整后的开发计划**

### **Week 1-2: Mock数据版本**
- [ ] 沉思模式界面搭建
- [ ] Mock数据准备（20-30篇高质量笔记）
- [ ] 基础召回算法实现
- [ ] 画布交互原型

### **Week 3-4: 核心体验打磨**
- [ ] 问题节点和对话功能
- [ ] 拖拽交互优化
- [ ] 笔记详情窗口
- [ ] 用户体验测试和迭代

### **Week 5-6: 数据持久化**
- [ ] IndexedDB集成
- [ ] 从沉淀模式保存功能
- [ ] 真实数据迁移
- [ ] 性能优化

### **Week 7-8: 高级功能**
- [ ] AI标签生成
- [ ] 语义搜索（可选）
- [ ] 导入导出功能
- [ ] 最终优化

---

> 本文档将作为沉思模式开发的指导文档，在开发过程中根据实际情况进行迭代更新。