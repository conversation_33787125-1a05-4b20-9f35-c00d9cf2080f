# 沉淀 (<PERSON>) - 你的 AI 记忆沉淀器

> 记录的过程 × 拥有的感觉 > 回顾的结果

**沉淀**是为**信息焦虑者**打造的**AI 记忆沉淀器**。它不是又一款需要你费心维护的知识管理工具，而是一个能带来「**知识心安感**」的**情绪价值产品**。

就像 [Plex](https://www.plex.tv/) 之于你的电影库，「沉淀」是你个人知识领域的媒体中心：你享受的不仅是消费信息，更是看着信息被自动整理、结构化，并最终**拥有一面属于自己的“知识海报墙”**的满足感。

---

## ✨ 核心特性

「沉淀」将知识处理流程重塑为三个核心环节：**收集沉淀**、**画布召回** 和 **节点加工**。

### 1️⃣ **收集 → 沉淀 (结构化笔记)**

轻松捕捉，无忧存储。

- **一键收集**: 通过浏览器插件快速收藏网页、PDF、文章等内容。收集后不打扰，内容自动进入“收集箱”。
- **AI 自动结构化**: AI 自动将收集的内容解析为结构化卡片，包含`标题`、`摘要`、`大纲`和`核心笔记`。
- **对话式编辑**: 借鉴 AI IDE 的思路，你无需手动修改大段文字。通过与 AI 对话，即可生成 `diff` 精确地更新笔记内容。

<br/>

### 2️⃣ **召回 → 画布 (知识再组织)**

让知识在对话中浮现。

- **画布式交互**: 每一次提问或新建任务，都会开启一个空白画布。
- **智能召回与连接**: AI 在画布中央生成回答的同时，会自动从你的知识库中召回相关的知识卡片，并用连线可视化地展示引用关系。
- **持续推荐**: 在后续的对话中，系统会持续推荐更多相关的卡片，供你随时拖入画布参与讨论。

<br/>

### 3️⃣ **加工 → 输出 (多模态内容生成)**

像剪辑视频一样创作新内容。

- **输出节点**: 在画布中，你可以添加不同类型的输出节点，例如 `PPT 节点`、`小红书图文节点`或`播客稿件节点`。
- **拖拽式生成**: 将知识卡片拖拽连接到输出节点，即可触发内容生成。
- **风格定制**: 每个节点都支持进一步的指令，如“帮我把这些内容生成一张可分享的长图”，轻松定制输出风格。

---

## 🧠 设计哲学

### 知识库：非文件夹式 → 多维索引式

告别繁琐的文件夹分类。我们使用**标签、时间、内容类型、模糊搜索**等多维度索引你的知识库。你的所有知识都存储在一个全局的个人向量数据库中，可以随时被任何任务跨时空召回。

### 交互：视频剪辑式工作流

我们将复杂的知识加工过程设计得像视频剪辑一样直观：
- **素材库 (知识库)**: 你所有的知识卡片。
- **项目篮 (召回区)**: AI 为当前任务自动挑选的相关卡片。
- **时间线 (画布)**: 你组织、加工、创作新内容的工作台。

---

## 🤖 AI 原生交互亮点

- **结构化笔记编辑**: AI 通过对话生成 `diff` 自动修改笔记，精准高效。
- **卡片召回 + 连线可视化**: 对话自动带出引用卡片并连线，上下文一目了然。
- **划词局部问答**: 在原文或卡片中划词，即可就该片段进行问答或重写。
- **输出节点交互**: 每类输出都是“拖入卡片 → 生成内容 → 调整风格 → 导出”的标准化流程。

---

## 🛠️ 技术栈

- [Next.js](https://nextjs.org/) – 前端框架
- [React](https://react.dev/) – UI 库
- [TypeScript](https://www.typescriptlang.org/) – 类型系统
- [Tailwind CSS](https://tailwindcss.com/) – 样式
- [OpenAI API](https://openai.com/blog/openai-api) – AI 能力

---

## 🚀 项目状态

当前项目正依据此产品蓝图积极开发中。
