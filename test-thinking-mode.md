# 思维模式测试指南

## 修复完成的功能

### 1. **画布缩放和拖动**
- 使用 **Ctrl/Cmd + 滚轮** 进行缩放（0.1x - 3x）
- 使用 **空格键 + 拖拽** 或 **鼠标中键拖拽** 移动画布
- 使用 **鼠标滚轮** 进行上下左右平移
- 快捷键：
  - `Ctrl/Cmd + +` 放大
  - `Ctrl/Cmd + -` 缩小
  - `Ctrl/Cmd + 0` 重置缩放
  - `F` 适应屏幕

### 2. **AI回答卡片样式优化**
- 采用更优雅的渐变背景和阴影效果
- 内容区域支持滚动，不会超出卡片
- 改进的加载动画和状态指示
- 优化的文字排版和代码高亮

### 3. **卡片拖动修复**
- 只能通过拖拽头部移动节点
- 拖动时跟手，无偏移
- 拖动时有缩放和透明度反馈

### 4. **召回卡片显示修复**
- 修复了knowledgeBase索引问题
- 召回的卡片会自动添加到本地知识库
- 支持动态更新和实时显示

### 5. **画布改进**
- 画布大小为8000x8000像素
- 添加了网格背景
- 空状态提示固定在视口中央

## 测试步骤

1. 打开思维模式页面
2. 点击"开始思考"按钮
3. 输入问题，例如："如何学习React？"
4. 等待AI召回相关卡片并生成回答
5. 测试拖动卡片和AI节点
6. 测试画布缩放和平移功能
7. 在AI节点中继续追问

## 已知问题

1. 召回的卡片可能需要完整的KnowledgeNote数据结构
2. 连接线动画可能需要进一步优化
3. 大量卡片时的性能优化待实现

## 下一步改进

1. 实现卡片的虚拟化渲染
2. 添加右键菜单功能
3. 优化连接线的渲染性能
4. 添加更多节点类型（PPT、小红书等）
5. 实现画布状态的持久化